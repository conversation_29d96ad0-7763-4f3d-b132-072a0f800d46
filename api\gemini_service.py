import requests
import json
import base64
import io
import logging
from PIL import Image
from config import Config

# Configure logging
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

# API URLs for Gemini
GEMINI_API_URL = Config.GEMINI_API_URL
GEMINI_VISION_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={Config.GEMINI_API_KEY}"

def get_medical_analysis(prediction_result):
    """
    Get medical analysis from Gemini API based on classification results

    Args:
        prediction_result (dict): Contains class_name, confidence, etc.

    Returns:
        dict: Contains summary and recommendations
    """
    # Déterminer le type de modèle utilisé
    model_type = prediction_result.get('model_type', 'brain')

    if model_type == 'oral':
        prompt = f"""
        As a dental health AI assistant, analyze the following oral disease classification result:

        - Detected condition: {prediction_result['class_name']}
        - Confidence: {prediction_result['confidence']}%
        - Description: {prediction_result['description']}

        Provide a brief summary of this oral condition and general recommendations for next steps.
        Format your response as JSON with 'summary' and 'recommendations' fields.
        Do not include markdown code blocks or JSON formatting in your response.
        """
    elif model_type == 'alzheimer':
        prompt = f"""
        As a neurology AI assistant specializing in Alzheimer's disease, analyze the following neuroimaging result:

        - Detected condition: {prediction_result['class_name']}
        - Confidence: {prediction_result['confidence']}%
        - Description: {prediction_result['description']}

        Provide a brief summary of this neurological condition and general recommendations for next steps.
        Format your response as JSON with 'summary' and 'recommendations' fields.
        Do not include markdown code blocks or JSON formatting in your response.
        """
    elif model_type == 'fracture':
        prompt = f"""
        As an orthopedic AI assistant specializing in bone fractures, analyze the following X-ray result:

        - Detected condition: {prediction_result['class_name']}
        - Confidence: {prediction_result['confidence']}%
        - Description: {prediction_result['description']}

        Provide a brief summary of this orthopedic condition and general recommendations for next steps.
        Format your response as JSON with 'summary' and 'recommendations' fields.
        Do not include markdown code blocks or JSON formatting in your response.
        """
    else:
        prompt = f"""
        As a medical AI assistant, analyze the following brain MRI scan result:

        - Detected condition: {prediction_result['class_name']}
        - Confidence: {prediction_result['confidence']}%
        - Description: {prediction_result['description']}

        Provide a brief summary of this condition and general recommendations for next steps.
        Format your response as JSON with 'summary' and 'recommendations' fields.
        Do not include markdown code blocks or JSON formatting in your response.
        """

    payload = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }]
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(GEMINI_API_URL, headers=headers, data=json.dumps(payload))
        response_data = response.json()

        # Extract text from response
        text_response = response_data["candidates"][0]["content"]["parts"][0]["text"]

        # Clean the response - remove markdown code blocks if present
        text_response = text_response.replace("```json", "").replace("```", "").strip()

        # Try to parse as JSON
        try:
            # Remove any leading/trailing non-JSON characters
            json_start = text_response.find('{')
            json_end = text_response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                text_response = text_response[json_start:json_end]

            json_response = json.loads(text_response)
            return json_response
        except:
            # If not valid JSON, create our own structure
            return {
                "summary": text_response[:text_response.find("\n\nRecommendations")] if "\n\nRecommendations" in text_response else text_response[:200],
                "recommendations": text_response[text_response.find("\n\nRecommendations")+16:] if "\n\nRecommendations" in text_response else ""
            }

    except Exception as e:
        print(f"Error calling Gemini API: {e}")
        return {
            "summary": "Unable to generate analysis at this time.",
            "recommendations": "Please consult with a medical professional for proper diagnosis."
        }

def chat_with_medical_assistant(user_message, context=None, model_type='brain'):
    """
    Chat with Gemini API about medical topics with enhanced error handling and response formatting

    Args:
        user_message (str): User's message
        context (str, optional): Previous conversation context
        model_type (str): Type of model ('brain', 'oral', 'alzheimer', or 'fracture')

    Returns:
        dict: Response with message, status, and metadata
    """
    # Définir les prompts système spécialisés
    system_prompts = {
        'oral': """Vous êtes un assistant spécialisé en santé dentaire et maladies bucco-dentaires.
        Fournissez des informations précises et utiles, mais rappelez toujours aux utilisateurs de consulter des professionnels de santé dentaire.
        Répondez en français de manière concise et informative. Si l'utilisateur pose une question qui nécessite un diagnostic médical, orientez-le vers un dentiste.""",

        'alzheimer': """Vous êtes un assistant spécialisé en neurologie et maladie d'Alzheimer.
        Fournissez des informations précises et utiles, mais rappelez toujours aux utilisateurs de consulter des neurologues et professionnels de santé.
        Répondez en français de manière concise et informative. Soyez empathique car ces sujets peuvent être sensibles.""",

        'fracture': """Vous êtes un assistant spécialisé en orthopédie et traumatologie osseuse.
        Fournissez des informations précises et utiles, mais rappelez toujours aux utilisateurs de consulter des chirurgiens orthopédistes et professionnels de santé.
        Répondez en français de manière concise et informative. En cas de traumatisme aigu, conseillez une consultation d'urgence.""",

        'brain': """Vous êtes un assistant médical spécialisé dans les conditions cérébrales et neurologiques.
        Fournissez des informations précises et utiles, mais rappelez toujours aux utilisateurs de consulter des professionnels de santé.
        Répondez en français de manière concise et informative. Soyez prudent avec les diagnostics et orientez vers des spécialistes."""
    }

    system_prompt = system_prompts.get(model_type, system_prompts['brain'])

    # Construire le prompt complet
    full_prompt = f"{system_prompt}\n\n"

    if context and context.strip():
        full_prompt += f"Historique de la conversation:\n{context}\n\n"

    full_prompt += f"Utilisateur: {user_message}\nAssistant:"

    # Préparer la requête
    payload = {
        "contents": [{
            "parts": [{
                "text": full_prompt
            }]
        }],
        "generationConfig": {
            "temperature": 0.7,
            "topK": 40,
            "topP": 0.95,
            "maxOutputTokens": 1024,
        },
        "safetySettings": [
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            }
        ]
    }

    headers = {
        "Content-Type": "application/json"
    }

    try:
        logger.info(f"Envoi de requête chat à Gemini API (type: {model_type})")
        response = requests.post(GEMINI_API_URL, headers=headers, data=json.dumps(payload), timeout=30)

        if response.status_code != 200:
            logger.error(f"Erreur API Gemini: {response.status_code} - {response.text}")
            return {
                "message": "Je rencontre des difficultés techniques. Veuillez réessayer dans quelques instants.",
                "status": "error",
                "error_code": response.status_code
            }

        response_data = response.json()

        # Vérifier la présence de la réponse
        if not response_data.get("candidates") or not response_data["candidates"]:
            logger.error("Réponse Gemini vide ou invalide")
            return {
                "message": "Je n'ai pas pu générer une réponse appropriée. Pouvez-vous reformuler votre question ?",
                "status": "error",
                "error_code": "empty_response"
            }

        # Extraire le texte de la réponse
        candidate = response_data["candidates"][0]
        if "content" not in candidate or "parts" not in candidate["content"]:
            logger.error("Structure de réponse Gemini invalide")
            return {
                "message": "Réponse mal formatée reçue. Veuillez réessayer.",
                "status": "error",
                "error_code": "invalid_format"
            }

        assistant_message = candidate["content"]["parts"][0]["text"].strip()

        # Vérifier si la réponse a été bloquée pour des raisons de sécurité
        if candidate.get("finishReason") == "SAFETY":
            logger.warning("Réponse bloquée par les filtres de sécurité Gemini")
            return {
                "message": "Je ne peux pas répondre à cette question pour des raisons de sécurité. Veuillez consulter directement un professionnel de santé.",
                "status": "blocked",
                "error_code": "safety_filter"
            }

        logger.info(f"Réponse chat générée avec succès (longueur: {len(assistant_message)} caractères)")

        return {
            "message": assistant_message,
            "status": "success",
            "model_type": model_type,
            "response_length": len(assistant_message)
        }

    except requests.exceptions.Timeout:
        logger.error("Timeout lors de l'appel à l'API Gemini")
        return {
            "message": "La réponse prend trop de temps. Veuillez réessayer avec une question plus simple.",
            "status": "error",
            "error_code": "timeout"
        }
    except requests.exceptions.ConnectionError:
        logger.error("Erreur de connexion à l'API Gemini")
        return {
            "message": "Problème de connexion au service. Vérifiez votre connexion internet et réessayez.",
            "status": "error",
            "error_code": "connection_error"
        }
    except json.JSONDecodeError as e:
        logger.error(f"Erreur de décodage JSON: {e}")
        return {
            "message": "Réponse invalide reçue du service. Veuillez réessayer.",
            "status": "error",
            "error_code": "json_decode_error"
        }
    except Exception as e:
        logger.error(f"Erreur inattendue lors de l'appel à l'API Gemini: {e}")
        return {
            "message": "Une erreur inattendue s'est produite. Veuillez réessayer plus tard.",
            "status": "error",
            "error_code": "unexpected_error"
        }

def get_chat_suggestions(model_type='brain'):
    """
    Génère des suggestions de questions pour démarrer une conversation

    Args:
        model_type (str): Type de modèle pour adapter les suggestions

    Returns:
        list: Liste de suggestions de questions
    """
    suggestions = {
        'brain': [
            "Quels sont les symptômes d'une tumeur cérébrale ?",
            "Comment interpréter une IRM cérébrale ?",
            "Quelles sont les différences entre les types de tumeurs cérébrales ?",
            "Quand faut-il consulter un neurologue ?"
        ],
        'oral': [
            "Comment prévenir les caries dentaires ?",
            "Quels sont les signes d'une maladie des gencives ?",
            "Comment maintenir une bonne hygiène bucco-dentaire ?",
            "Que faire en cas de douleur dentaire ?"
        ],
        'alzheimer': [
            "Quels sont les premiers signes de la maladie d'Alzheimer ?",
            "Comment différencier le vieillissement normal des troubles cognitifs ?",
            "Quels examens permettent de diagnostiquer Alzheimer ?",
            "Comment accompagner une personne atteinte d'Alzheimer ?"
        ],
        'fracture': [
            "Comment reconnaître une fracture ?",
            "Quels sont les premiers secours en cas de fracture ?",
            "Combien de temps faut-il pour qu'une fracture guérisse ?",
            "Quand reprendre le sport après une fracture ?"
        ]
    }

    return suggestions.get(model_type, suggestions['brain'])


def detect_mri_scan(image_data, image_format='JPEG'):
    """
    Detect if an uploaded image is an MRI scan using Gemini Vision API

    Args:
        image_data: Image data (PIL Image, file path, or base64 string)
        image_format: Format of the image ('JPEG', 'PNG', etc.)

    Returns:
        dict: Detection results with is_mri, confidence, and metadata
    """
    try:
        logger.info("Starting MRI scan detection")

        # Process image data
        if isinstance(image_data, str):
            if image_data.startswith('data:image'):
                # Handle base64 data URL
                header, encoded = image_data.split(',', 1)
                image_bytes = base64.b64decode(encoded)
                image = Image.open(io.BytesIO(image_bytes))
            elif image_data.startswith('/') or '\\' in image_data:
                # Handle file path
                image = Image.open(image_data)
            else:
                # Handle base64 string without header
                image_bytes = base64.b64decode(image_data)
                image = Image.open(io.BytesIO(image_bytes))
        elif isinstance(image_data, Image.Image):
            image = image_data
        else:
            raise ValueError("Unsupported image data format")

        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # Convert PIL image to bytes for Gemini API
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format=image_format)
        img_byte_arr = img_byte_arr.getvalue()

        # Encode image to base64
        image_base64 = base64.b64encode(img_byte_arr).decode('utf-8')

        # Create the prompt for MRI detection
        prompt = """
        Analyze this medical image and determine if it is an MRI (Magnetic Resonance Imaging) scan.

        Consider the following characteristics of MRI scans:
        - Grayscale or specific color mapping typical of MRI
        - Cross-sectional view of body parts (brain, spine, joints, organs)
        - High contrast between different tissue types
        - Characteristic MRI artifacts and image quality
        - Anatomical structures visible in MRI format
        - Absence of bone structures (which appear dark in MRI)

        Provide your analysis in the following JSON format:
        {
            "is_mri": true/false,
            "confidence": 0.0-1.0,
            "detected_type": "description of what type of medical image this appears to be",
            "anatomical_region": "if MRI, specify the body region (brain, spine, knee, etc.)",
            "reasoning": "brief explanation of your decision",
            "image_characteristics": "description of key visual features observed"
        }

        Be precise and conservative in your assessment. Only classify as MRI if you are confident.
        """

        # Prepare payload for Gemini Vision API
        payload = {
            "contents": [{
                "parts": [
                    {"text": prompt},
                    {
                        "inline_data": {
                            "mime_type": f"image/{image_format.lower()}",
                            "data": image_base64
                        }
                    }
                ]
            }]
        }

        headers = {
            "Content-Type": "application/json"
        }

        # Generate response
        response = requests.post(GEMINI_VISION_API_URL, headers=headers, data=json.dumps(payload))

        # Parse the response
        if response.status_code != 200:
            logger.error(f"Gemini API error: {response.status_code} - {response.text}")
            raise Exception(f"Gemini API error: {response.status_code}")

        response_data = response.json()
        response_text = response_data["candidates"][0]["content"]["parts"][0]["text"].strip()
        logger.info(f"Gemini response: {response_text}")

        # Clean and parse JSON response
        response_text = response_text.replace("```json", "").replace("```", "").strip()

        # Find JSON content
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1

        if json_start >= 0 and json_end > json_start:
            json_text = response_text[json_start:json_end]
            try:
                result = json.loads(json_text)

                # Validate and normalize the response
                normalized_result = {
                    "is_mri": bool(result.get("is_mri", False)),
                    "confidence": float(result.get("confidence", 0.0)),
                    "detected_type": str(result.get("detected_type", "Unknown")),
                    "anatomical_region": str(result.get("anatomical_region", "Not specified")),
                    "reasoning": str(result.get("reasoning", "No reasoning provided")),
                    "image_characteristics": str(result.get("image_characteristics", "No characteristics noted")),
                    "api_status": "success"
                }

                logger.info(f"MRI detection completed: {normalized_result['is_mri']} with confidence {normalized_result['confidence']}")
                return normalized_result

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                return _create_fallback_response(response_text)
        else:
            logger.error("No valid JSON found in response")
            return _create_fallback_response(response_text)

    except Exception as e:
        logger.error(f"Error in MRI detection: {str(e)}")
        return {
            "is_mri": False,
            "confidence": 0.0,
            "detected_type": "Error during analysis",
            "anatomical_region": "Unknown",
            "reasoning": f"Analysis failed: {str(e)}",
            "image_characteristics": "Could not analyze image",
            "api_status": "error",
            "error": str(e)
        }


def _create_fallback_response(response_text):
    """
    Create a fallback response when JSON parsing fails
    """
    # Simple keyword-based analysis as fallback
    text_lower = response_text.lower()
    is_mri = any(keyword in text_lower for keyword in ['mri', 'magnetic resonance', 'true'])

    confidence = 0.3 if is_mri else 0.1  # Low confidence for fallback

    return {
        "is_mri": is_mri,
        "confidence": confidence,
        "detected_type": "Analysis incomplete",
        "anatomical_region": "Unknown",
        "reasoning": "Fallback analysis due to parsing error",
        "image_characteristics": response_text[:200] + "..." if len(response_text) > 200 else response_text,
        "api_status": "partial_success"
    }


def analyze_medical_image_with_vision(image_data, analysis_type="general", image_format='JPEG'):
    """
    General medical image analysis using Gemini Vision API

    Args:
        image_data: Image data (PIL Image, file path, or base64 string)
        analysis_type: Type of analysis ('mri_detection', 'general', 'detailed')
        image_format: Format of the image

    Returns:
        dict: Analysis results
    """
    if analysis_type == "mri_detection":
        return detect_mri_scan(image_data, image_format)

    # For other analysis types, implement additional functionality here
    return {"error": "Analysis type not implemented"}


