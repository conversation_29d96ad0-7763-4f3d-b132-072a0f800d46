"""
Chat Manager Mo<PERSON>le
<PERSON>ère les sessions de chat, l'historique des conversations et la limitation du contexte
"""

import uuid
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ChatMessage:
    """Représente un message dans une conversation"""
    
    def __init__(self, role: str, content: str, timestamp: Optional[datetime] = None):
        self.role = role  # 'user' ou 'assistant'
        self.content = content
        self.timestamp = timestamp or datetime.now()
        self.id = str(uuid.uuid4())
    
    def to_dict(self) -> dict:
        return {
            'id': self.id,
            'role': self.role,
            'content': self.content,
            'timestamp': self.timestamp.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ChatMessage':
        msg = cls(data['role'], data['content'])
        msg.id = data['id']
        msg.timestamp = datetime.fromisoformat(data['timestamp'])
        return msg

class ChatSession:
    """Gère une session de chat individuelle"""
    
    def __init__(self, session_id: str, model_type: str = 'brain', max_context_length: int = 4000):
        self.session_id = session_id
        self.model_type = model_type
        self.messages: List[ChatMessage] = []
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.max_context_length = max_context_length
        self.metadata = {}
    
    def add_message(self, role: str, content: str) -> ChatMessage:
        """Ajoute un message à la session"""
        message = ChatMessage(role, content)
        self.messages.append(message)
        self.last_activity = datetime.now()
        
        # Nettoyer le contexte si nécessaire
        self._trim_context_if_needed()
        
        logger.info(f"Message ajouté à la session {self.session_id}: {role}")
        return message
    
    def get_context(self, max_messages: int = 10) -> str:
        """Génère le contexte de conversation pour l'API"""
        # Prendre les derniers messages
        recent_messages = self.messages[-max_messages:] if len(self.messages) > max_messages else self.messages
        
        context_parts = []
        for msg in recent_messages:
            context_parts.append(f"{msg.role.capitalize()}: {msg.content}")
        
        context = "\n".join(context_parts)
        
        # Vérifier la longueur du contexte
        if len(context) > self.max_context_length:
            # Réduire le nombre de messages si le contexte est trop long
            return self._get_truncated_context()
        
        return context
    
    def _get_truncated_context(self) -> str:
        """Génère un contexte tronqué pour respecter la limite de longueur"""
        context = ""
        messages_to_include = []
        
        # Commencer par les messages les plus récents
        for msg in reversed(self.messages):
            temp_context = f"{msg.role.capitalize()}: {msg.content}\n" + context
            if len(temp_context) <= self.max_context_length:
                context = temp_context
                messages_to_include.insert(0, msg)
            else:
                break
        
        # Si on a trop peu de messages, inclure au moins les 2 derniers
        if len(messages_to_include) < 2 and len(self.messages) >= 2:
            last_two = self.messages[-2:]
            context = "\n".join([f"{msg.role.capitalize()}: {msg.content}" for msg in last_two])
            # Tronquer si nécessaire
            if len(context) > self.max_context_length:
                context = context[:self.max_context_length] + "..."
        
        return context
    
    def _trim_context_if_needed(self):
        """Supprime les anciens messages si la session devient trop longue"""
        max_messages = 50  # Limite du nombre de messages par session
        if len(self.messages) > max_messages:
            # Garder les 30 messages les plus récents
            self.messages = self.messages[-30:]
            logger.info(f"Session {self.session_id}: Contexte nettoyé, {len(self.messages)} messages conservés")
    
    def get_summary(self) -> dict:
        """Retourne un résumé de la session"""
        return {
            'session_id': self.session_id,
            'model_type': self.model_type,
            'message_count': len(self.messages),
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'duration_minutes': (self.last_activity - self.created_at).total_seconds() / 60
        }
    
    def to_dict(self) -> dict:
        """Sérialise la session en dictionnaire"""
        return {
            'session_id': self.session_id,
            'model_type': self.model_type,
            'messages': [msg.to_dict() for msg in self.messages],
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'max_context_length': self.max_context_length,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ChatSession':
        """Désérialise une session depuis un dictionnaire"""
        session = cls(
            data['session_id'],
            data.get('model_type', 'brain'),
            data.get('max_context_length', 4000)
        )
        session.created_at = datetime.fromisoformat(data['created_at'])
        session.last_activity = datetime.fromisoformat(data['last_activity'])
        session.metadata = data.get('metadata', {})
        session.messages = [ChatMessage.from_dict(msg_data) for msg_data in data.get('messages', [])]
        return session

class ChatManager:
    """Gestionnaire principal des sessions de chat"""
    
    def __init__(self, session_timeout_hours: int = 24):
        self.sessions: Dict[str, ChatSession] = {}
        self.session_timeout = timedelta(hours=session_timeout_hours)
        self.last_cleanup = datetime.now()
    
    def create_session(self, model_type: str = 'brain') -> str:
        """Crée une nouvelle session de chat"""
        session_id = str(uuid.uuid4())
        session = ChatSession(session_id, model_type)
        self.sessions[session_id] = session
        
        logger.info(f"Nouvelle session créée: {session_id} (type: {model_type})")
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ChatSession]:
        """Récupère une session existante"""
        self._cleanup_expired_sessions()
        return self.sessions.get(session_id)
    
    def get_or_create_session(self, session_id: Optional[str], model_type: str = 'brain') -> Tuple[str, ChatSession]:
        """Récupère une session existante ou en crée une nouvelle"""
        if session_id and session_id in self.sessions:
            session = self.sessions[session_id]
            session.last_activity = datetime.now()
            return session_id, session
        else:
            new_session_id = self.create_session(model_type)
            return new_session_id, self.sessions[new_session_id]
    
    def add_message(self, session_id: str, role: str, content: str) -> Optional[ChatMessage]:
        """Ajoute un message à une session"""
        session = self.get_session(session_id)
        if session:
            return session.add_message(role, content)
        return None
    
    def get_context(self, session_id: str, max_messages: int = 10) -> str:
        """Récupère le contexte d'une session"""
        session = self.get_session(session_id)
        if session:
            return session.get_context(max_messages)
        return ""
    
    def delete_session(self, session_id: str) -> bool:
        """Supprime une session"""
        if session_id in self.sessions:
            del self.sessions[session_id]
            logger.info(f"Session supprimée: {session_id}")
            return True
        return False
    
    def clear_session_history(self, session_id: str) -> bool:
        """Efface l'historique d'une session"""
        session = self.get_session(session_id)
        if session:
            session.messages.clear()
            session.last_activity = datetime.now()
            logger.info(f"Historique effacé pour la session: {session_id}")
            return True
        return False
    
    def get_session_stats(self) -> dict:
        """Retourne les statistiques des sessions"""
        active_sessions = len(self.sessions)
        total_messages = sum(len(session.messages) for session in self.sessions.values())
        
        model_types = {}
        for session in self.sessions.values():
            model_type = session.model_type
            model_types[model_type] = model_types.get(model_type, 0) + 1
        
        return {
            'active_sessions': active_sessions,
            'total_messages': total_messages,
            'model_types': model_types,
            'last_cleanup': self.last_cleanup.isoformat()
        }
    
    def _cleanup_expired_sessions(self):
        """Nettoie les sessions expirées"""
        now = datetime.now()
        
        # Ne nettoyer qu'une fois par heure
        if now - self.last_cleanup < timedelta(hours=1):
            return
        
        expired_sessions = []
        for session_id, session in self.sessions.items():
            if now - session.last_activity > self.session_timeout:
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.sessions[session_id]
            logger.info(f"Session expirée supprimée: {session_id}")
        
        self.last_cleanup = now
        
        if expired_sessions:
            logger.info(f"Nettoyage terminé: {len(expired_sessions)} sessions expirées supprimées")

# Instance globale du gestionnaire de chat
chat_manager = ChatManager()
